#include "SerialInterface.h"

extern SomfyShadeController somfy;

SerialInterface::SerialInterface() {
  bufferIndex = 0;
  commandReady = false;
  memset(inputBuffer, 0, sizeof(inputBuffer));
}

void SerialInterface::begin(uint32_t baudRate) {
  Serial.begin(baudRate);
  Serial.println();
  Serial.println("=== ESPSomfy-RTS Serial Interface ===");
  Serial.println("Type 'help' for available commands");
  Serial.print("> ");
}

void SerialInterface::loop() {
  processInput();
}

void SerialInterface::processInput() {
  while (Serial.available()) {
    char c = Serial.read();

    // If in pairing mode, any key exits pairing mode
    if (pairingMode) {
      Serial.println("\nExiting pairing mode.");
      pairingMode = false;
      pairingShadeId = 0;
      Serial.print("> ");
      return;
    }

    // If in listening mode, any key exits listening mode
    if (listeningMode) {
      Serial.println("\nExiting listening mode.");
      listeningMode = false;
      Serial.print("> ");
      return;
    }

    // If in scanning mode, any key exits scanning mode
    if (scanningMode) {
      Serial.println("\nExiting scanning mode.");
      Serial.printf("Discovered %d unknown remotes during scan.\n", discoveredRemoteCount);
      scanningMode = false;
      Serial.print("> ");
      return;
    }

    if (c == '\n' || c == '\r') {
      if (bufferIndex > 0) {
        inputBuffer[bufferIndex] = '\0';
        parseCommand(inputBuffer);
        bufferIndex = 0;
        memset(inputBuffer, 0, sizeof(inputBuffer));
        Serial.print("> ");
      }
    } else if (c == '\b' || c == 127) { // Backspace
      if (bufferIndex > 0) {
        bufferIndex--;
        Serial.print("\b \b");
      }
    } else if (bufferIndex < sizeof(inputBuffer) - 1 && c >= 32 && c <= 126) {
      inputBuffer[bufferIndex++] = c;
      Serial.print(c);
    }
  }
}

void SerialInterface::parseCommand(const char* command) {
  Serial.println(); // New line after command
  
  // Convert to lowercase and trim
  String cmd = String(command);
  cmd.trim();
  cmd.toLowerCase();
  
  if (cmd.length() == 0) return;
  
  // Split command into parts
  int spaceIndex = cmd.indexOf(' ');
  String mainCmd = (spaceIndex > 0) ? cmd.substring(0, spaceIndex) : cmd;
  String params = (spaceIndex > 0) ? cmd.substring(spaceIndex + 1) : "";
  
  // Handle commands
  if (mainCmd == "help" || mainCmd == "h") {
    printHelp();
  } else if (mainCmd == "list" || mainCmd == "ls") {
    handleListCommand(params.c_str());
  } else if (mainCmd == "info") {
    handleInfoCommand(params.c_str());
  } else if (mainCmd == "pair") {
    handlePairCommand(params.c_str());
  } else if (mainCmd == "add") {
    handleAddCommand(params.c_str());
  } else if (mainCmd == "delete" || mainCmd == "del") {
    handleDeleteCommand(params.c_str());
  } else if (mainCmd == "set") {
    handleSetCommand(params.c_str());
  } else if (mainCmd == "unpair") {
    handleUnpairCommand(params.c_str());
  } else if (mainCmd == "listen") {
    handleListenCommand(params.c_str());
  } else if (mainCmd == "scan") {
    handleScanCommand(params.c_str());
  } else if (mainCmd == "test") {
    handleTestCommand(params.c_str());
  } else if (mainCmd == "status") {
    handleStatusCommand();
  } else if (mainCmd.startsWith("shade") && mainCmd.length() > 5) {
    // Handle shade commands like "shade1", "shade2", etc.
    uint8_t shadeId = parseShadeId(mainCmd.c_str() + 5);
    if (isValidShadeId(shadeId)) {
      handleShadeCommand(shadeId, params.c_str());
    } else {
      Serial.println("Invalid shade ID");
    }
  } else if (mainCmd.toInt() > 0) {
    // Handle numeric shade ID
    uint8_t shadeId = mainCmd.toInt();
    if (isValidShadeId(shadeId)) {
      handleShadeCommand(shadeId, params.c_str());
    } else {
      Serial.println("Invalid shade ID");
    }
  } else {
    Serial.println("Unknown command. Type 'help' for available commands.");
  }
}

void SerialInterface::printHelp() {
  Serial.println("Available commands:");
  Serial.println("  help, h                    - Show this help");
  Serial.println("  list, ls [shades|groups]   - List shades or groups");
  Serial.println("  info <id>                  - Show detailed info for shade/group");
  Serial.println("  pair <id>                  - Enter pairing mode for shade");
  Serial.println("  unpair <id>                - Unpair shade from remote");
  Serial.println("  listen                     - Listen for remote commands");
  Serial.println("  scan                       - Scan for unknown remotes");
  Serial.println("  test <id>                  - Test shade communication");
  Serial.println("  status                     - Show system status");
  Serial.println("  add shade <name>           - Add new shade");
  Serial.println("  delete <id>                - Delete shade/group");
  Serial.println("  set <id> <property> <val>  - Set shade property");
  Serial.println();
  Serial.println("Shade control commands:");
  Serial.println("  <id> up                    - Move shade up");
  Serial.println("  <id> down                  - Move shade down");
  Serial.println("  <id> my                    - Move to MY position");
  Serial.println("  <id> stop                  - Stop movement");
  Serial.println("  <id> pos <0-100>           - Move to position %");
  Serial.println("  <id> tilt <0-100>          - Set tilt position %");
  Serial.println("  <id> setmy                 - Set current pos as MY");
  Serial.println();
  Serial.println("Examples:");
  Serial.println("  1 up                       - Move shade 1 up");
  Serial.println("  shade2 pos 50              - Move shade 2 to 50%");
  Serial.println("  list shades                - List all shades");
  Serial.println("  pair 1                     - Start pairing mode for shade 1");
}

void SerialInterface::printShadeList() {
  Serial.println("Configured shades:");
  Serial.println("ID | Name                | Address    | Paired | Position | Type");
  Serial.println("---|---------------------|------------|--------|----------|--------");
  
  bool found = false;
  for (uint8_t i = 0; i < SOMFY_MAX_SHADES; i++) {
    SomfyShade* shade = &somfy.shades[i];
    if (shade->getShadeId() != 255) {
      found = true;
      Serial.printf("%2d | %-19s | 0x%08X | %-6s | %6.1f%% | %s\n",
        shade->getShadeId(),
        shade->name,
        shade->getRemoteAddress(),
        shade->paired ? "Yes" : "No",
        shade->transformPosition(shade->currentPos),
        shade->shadeType == shade_types::roller ? "Roller" :
        shade->shadeType == shade_types::blind ? "Blind" :
        shade->shadeType == shade_types::awning ? "Awning" : "Other"
      );
    }
  }
  
  if (!found) {
    Serial.println("No shades configured.");
  }
}

void SerialInterface::printShadeInfo(uint8_t shadeId) {
  SomfyShade* shade = somfy.getShadeById(shadeId);
  if (!shade) {
    Serial.printf("Shade %d not found.\n", shadeId);
    return;
  }
  
  Serial.printf("Shade %d Information:\n", shadeId);
  Serial.printf("  Name: %s\n", shade->name);
  Serial.printf("  Remote Address: 0x%08X\n", shade->getRemoteAddress());
  Serial.printf("  Paired: %s\n", shade->paired ? "Yes" : "No");
  Serial.printf("  Position: %.1f%%\n", shade->transformPosition(shade->currentPos));
  Serial.printf("  Target: %.1f%%\n", shade->transformPosition(shade->target));
  Serial.printf("  Direction: %s\n", 
    shade->direction == -1 ? "Up" : 
    shade->direction == 1 ? "Down" : "Stopped");
  Serial.printf("  MY Position: %.1f%%\n", 
    shade->myPos >= 0 ? shade->transformPosition(shade->myPos) : -1);
  
  if (shade->tiltType != tilt_types::none) {
    Serial.printf("  Tilt Position: %.1f%%\n", shade->transformPosition(shade->currentTiltPos));
    Serial.printf("  Tilt Target: %.1f%%\n", shade->transformPosition(shade->tiltTarget));
  }
  
  Serial.printf("  Up Time: %d ms\n", shade->upTime);
  Serial.printf("  Down Time: %d ms\n", shade->downTime);
  Serial.printf("  Rolling Code: %d\n", shade->lastRollingCode);
}

void SerialInterface::printRemoteInfo(uint32_t address, uint16_t rollingCode, somfy_commands cmd) {
  Serial.printf("Remote detected: Address=0x%08X, Code=%d, Command=%s\n",
    address, rollingCode, translateSomfyCommand(cmd).c_str());
}

uint8_t SerialInterface::parseShadeId(const char* str) {
  return (uint8_t)atoi(str);
}

float SerialInterface::parseFloat(const char* str) {
  return atof(str);
}

bool SerialInterface::isValidShadeId(uint8_t shadeId) {
  return shadeId > 0 && shadeId <= SOMFY_MAX_SHADES && somfy.getShadeById(shadeId) != nullptr;
}

void SerialInterface::onRemoteReceived(uint32_t address, uint16_t rollingCode, somfy_commands cmd, int8_t rssi) {
  // Check if this remote is already known
  SomfyShade* knownShade = somfy.findShadeByRemoteAddress(address);

  // Handle scanning mode - track unknown remotes
  if (scanningMode && !knownShade) {
    // Look for this remote in our discovered list
    int remoteIndex = -1;
    for (uint8_t i = 0; i < discoveredRemoteCount; i++) {
      if (discoveredRemotes[i].address == address) {
        remoteIndex = i;
        break;
      }
    }

    if (remoteIndex == -1 && discoveredRemoteCount < MAX_DISCOVERED_REMOTES) {
      // New unknown remote discovered
      remoteIndex = discoveredRemoteCount++;
      discoveredRemotes[remoteIndex].address = address;
      discoveredRemotes[remoteIndex].commandCount = 0;
      Serial.printf("*** NEW REMOTE DISCOVERED: 0x%08X ***\n", address);
    }

    if (remoteIndex >= 0) {
      discoveredRemotes[remoteIndex].lastRollingCode = rollingCode;
      discoveredRemotes[remoteIndex].lastSeen = millis();
      discoveredRemotes[remoteIndex].commandCount++;

      Serial.printf("UNKNOWN: 0x%08X | Code: %5d | Cmd: %-8s | RSSI: %3d dBm | Count: %d\n",
        address, rollingCode, translateSomfyCommand(cmd).c_str(), rssi,
        discoveredRemotes[remoteIndex].commandCount);
    }
  } else if (listeningMode || !knownShade) {
    // Show detailed remote activity in listening mode or for unknown remotes
    Serial.printf("REMOTE: 0x%08X | Code: %5d | Cmd: %-8s | RSSI: %3d dBm",
      address, rollingCode, translateSomfyCommand(cmd).c_str(), rssi);

    if (knownShade) {
      Serial.printf(" | Shade: %d (%s)", knownShade->getShadeId(), knownShade->name);
    } else {
      Serial.print(" | UNKNOWN");
    }
    Serial.println();
  } else if (knownShade) {
    // For known remotes, show abbreviated info
    Serial.printf("Shade %d: %s (0x%08X)\n",
      knownShade->getShadeId(), translateSomfyCommand(cmd).c_str(), address);
  }

  // Handle pairing mode
  if (pairingMode && cmd == somfy_commands::Prog) {
    SomfyShade* shade = somfy.getShadeById(pairingShadeId);
    if (shade) {
      // Check if this remote is already paired to another shade
      SomfyShade* existingShade = somfy.findShadeByRemoteAddress(address);
      if (existingShade && existingShade->getShadeId() != pairingShadeId) {
        Serial.printf("WARNING: Remote 0x%08X is already paired to shade %d (%s)\n",
          address, existingShade->getShadeId(), existingShade->name);
        Serial.println("Continue pairing? (y/n)");
        // For now, we'll continue with the pairing
      }

      // Pair the remote to the shade
      shade->setRemoteAddress(address);
      shade->setRollingCode(rollingCode);
      shade->paired = true;

      // Save the configuration
      if (shade->save()) {
        Serial.printf("SUCCESS: Shade %d (%s) paired to remote 0x%08X\n",
          pairingShadeId, shade->name, address);
        Serial.printf("Rolling code set to: %d\n", rollingCode);

        // Send a test command to confirm pairing
        Serial.println("Sending test MY command to confirm pairing...");
        shade->sendCommand(somfy_commands::My);

        // Call the pairing callback
        onShadePaired(pairingShadeId, address);
      } else {
        Serial.println("ERROR: Failed to save shade configuration");
      }

      // Exit pairing mode
      pairingMode = false;
      pairingShadeId = 0;
    }
  }

  // Check for pairing timeout (30 seconds)
  if (pairingMode && (millis() - pairingStartTime) > 30000) {
    Serial.println("Pairing timeout. Exiting pairing mode.");
    pairingMode = false;
    pairingShadeId = 0;
  }
}

void SerialInterface::onShadeStateChanged(uint8_t shadeId, float position, float tiltPosition, int8_t direction) {
  Serial.printf("SHADE %d: Pos=%.1f%% Tilt=%.1f%% Dir=%s\n",
    shadeId, position, tiltPosition,
    direction == -1 ? "Up" : direction == 1 ? "Down" : "Stop");
}

void SerialInterface::onShadePaired(uint8_t shadeId, uint32_t address) {
  Serial.printf("PAIRED: Shade %d paired to remote 0x%08X\n", shadeId, address);
}

void SerialInterface::handleShadeCommand(uint8_t shadeId, const char* command) {
  SomfyShade* shade = somfy.getShadeById(shadeId);
  if (!shade) {
    Serial.printf("Shade %d not found.\n", shadeId);
    return;
  }

  String cmd = String(command);
  cmd.trim();
  cmd.toLowerCase();

  if (cmd == "up" || cmd == "u") {
    Serial.printf("Moving shade %d UP\n", shadeId);
    shade->sendCommand(somfy_commands::Up);
  } else if (cmd == "down" || cmd == "d") {
    Serial.printf("Moving shade %d DOWN\n", shadeId);
    shade->sendCommand(somfy_commands::Down);
  } else if (cmd == "my" || cmd == "m") {
    Serial.printf("Moving shade %d to MY position\n", shadeId);
    shade->sendCommand(somfy_commands::My);
  } else if (cmd == "stop" || cmd == "s") {
    Serial.printf("Stopping shade %d\n", shadeId);
    shade->sendCommand(somfy_commands::My);
  } else if (cmd == "setmy") {
    Serial.printf("Setting MY position for shade %d\n", shadeId);
    shade->setMyPosition(shade->currentPos, shade->currentTiltPos);
  } else if (cmd.startsWith("pos ")) {
    float position = parseFloat(cmd.c_str() + 4);
    if (position >= 0 && position <= 100) {
      Serial.printf("Moving shade %d to position %.1f%%\n", shadeId, position);
      shade->moveToTarget(position);
    } else {
      Serial.println("Position must be between 0 and 100");
    }
  } else if (cmd.startsWith("tilt ")) {
    float tilt = parseFloat(cmd.c_str() + 5);
    if (tilt >= 0 && tilt <= 100) {
      Serial.printf("Setting shade %d tilt to %.1f%%\n", shadeId, tilt);
      shade->moveToTiltTarget(tilt);
    } else {
      Serial.println("Tilt must be between 0 and 100");
    }
  } else if (cmd == "info" || cmd == "status") {
    printShadeInfo(shadeId);
  } else {
    Serial.printf("Unknown shade command: %s\n", command);
    Serial.println("Available: up, down, my, stop, pos <0-100>, tilt <0-100>, setmy, info");
  }
}

void SerialInterface::handlePairCommand(const char* params) {
  String param = String(params);
  param.trim();

  if (param.length() == 0) {
    Serial.println("Usage: pair <shade_id>");
    Serial.println("Example: pair 1");
    return;
  }

  uint8_t shadeId = parseShadeId(param.c_str());
  if (!isValidShadeId(shadeId)) {
    Serial.println("Invalid shade ID. Use 'list shades' to see available shades.");
    return;
  }

  SomfyShade* shade = somfy.getShadeById(shadeId);
  if (!shade) {
    Serial.printf("Shade %d not found.\n", shadeId);
    return;
  }

  Serial.printf("Pairing mode for shade %d (%s)\n", shadeId, shade->name);
  Serial.println("Instructions:");
  Serial.println("1. Press and hold the PROG button on your Somfy remote for 3 seconds");
  Serial.println("2. The shade should jog to confirm pairing");
  Serial.println("3. Wait for confirmation message");
  Serial.println("4. Press any key to exit pairing mode");
  Serial.println();
  Serial.println("Listening for PROG commands...");

  // Set a flag to indicate we're in pairing mode for this shade
  // The onRemoteReceived function will handle the actual pairing
  pairingMode = true;
  pairingShadeId = shadeId;
  pairingStartTime = millis();
}

void SerialInterface::handleListCommand(const char* params) {
  String param = String(params);
  param.trim();
  param.toLowerCase();

  if (param == "" || param == "shades") {
    printShadeList();
  } else if (param == "groups") {
    Serial.println("Groups not yet implemented in serial interface");
  } else {
    Serial.println("Usage: list [shades|groups]");
  }
}

void SerialInterface::handleInfoCommand(const char* params) {
  String param = String(params);
  param.trim();

  if (param.length() == 0) {
    Serial.println("Usage: info <shade_id>");
    return;
  }

  uint8_t shadeId = parseShadeId(param.c_str());
  if (shadeId > 0) {
    printShadeInfo(shadeId);
  } else {
    Serial.println("Invalid shade ID");
  }
}

void SerialInterface::handleAddCommand(const char* params) {
  String param = String(params);
  param.trim();

  if (param.startsWith("shade ")) {
    String name = param.substring(6);
    name.trim();

    if (name.length() == 0) {
      Serial.println("Usage: add shade <name>");
      return;
    }

    SomfyShade* shade = somfy.addShade();
    if (shade) {
      strncpy(shade->name, name.c_str(), sizeof(shade->name) - 1);
      shade->name[sizeof(shade->name) - 1] = '\0';
      somfy.commit();
      Serial.printf("Added shade %d: %s\n", shade->getShadeId(), shade->name);
    } else {
      Serial.println("Failed to add shade (maximum reached?)");
    }
  } else {
    Serial.println("Usage: add shade <name>");
  }
}

void SerialInterface::handleDeleteCommand(const char* params) {
  String param = String(params);
  param.trim();

  if (param.length() == 0) {
    Serial.println("Usage: delete <shade_id>");
    return;
  }

  uint8_t shadeId = parseShadeId(param.c_str());
  if (shadeId > 0 && isValidShadeId(shadeId)) {
    if (somfy.deleteShade(shadeId)) {
      Serial.printf("Deleted shade %d\n", shadeId);
    } else {
      Serial.printf("Failed to delete shade %d\n", shadeId);
    }
  } else {
    Serial.println("Invalid shade ID");
  }
}

void SerialInterface::handleSetCommand(const char* params) {
  // Parse: set <id> <property> <value>
  String param = String(params);
  param.trim();

  int space1 = param.indexOf(' ');
  int space2 = param.indexOf(' ', space1 + 1);

  if (space1 == -1 || space2 == -1) {
    Serial.println("Usage: set <shade_id> <property> <value>");
    Serial.println("Properties: name, uptime, downtime, tilttime");
    return;
  }

  uint8_t shadeId = parseShadeId(param.substring(0, space1).c_str());
  String property = param.substring(space1 + 1, space2);
  String value = param.substring(space2 + 1);

  property.toLowerCase();

  SomfyShade* shade = somfy.getShadeById(shadeId);
  if (!shade) {
    Serial.printf("Shade %d not found\n", shadeId);
    return;
  }

  if (property == "name") {
    strncpy(shade->name, value.c_str(), sizeof(shade->name) - 1);
    shade->name[sizeof(shade->name) - 1] = '\0';
    Serial.printf("Set shade %d name to: %s\n", shadeId, shade->name);
  } else if (property == "uptime") {
    shade->upTime = value.toInt();
    Serial.printf("Set shade %d up time to: %d ms\n", shadeId, shade->upTime);
  } else if (property == "downtime") {
    shade->downTime = value.toInt();
    Serial.printf("Set shade %d down time to: %d ms\n", shadeId, shade->downTime);
  } else if (property == "tilttime") {
    shade->tiltTime = value.toInt();
    Serial.printf("Set shade %d tilt time to: %d ms\n", shadeId, shade->tiltTime);
  } else {
    Serial.printf("Unknown property: %s\n", property.c_str());
    Serial.println("Available: name, uptime, downtime, tilttime");
    return;
  }

  somfy.commit();
}

void SerialInterface::handleUnpairCommand(const char* params) {
  String param = String(params);
  param.trim();

  if (param.length() == 0) {
    Serial.println("Usage: unpair <shade_id>");
    return;
  }

  uint8_t shadeId = parseShadeId(param.c_str());
  if (!isValidShadeId(shadeId)) {
    Serial.println("Invalid shade ID");
    return;
  }

  SomfyShade* shade = somfy.getShadeById(shadeId);
  if (!shade) {
    Serial.printf("Shade %d not found\n", shadeId);
    return;
  }

  if (!shade->paired) {
    Serial.printf("Shade %d is not paired to any remote\n", shadeId);
    return;
  }

  uint32_t oldAddress = shade->getRemoteAddress();

  // Clear the pairing
  shade->setRemoteAddress(0);
  shade->setRollingCode(0);
  shade->paired = false;

  if (shade->save()) {
    Serial.printf("SUCCESS: Shade %d (%s) unpaired from remote 0x%08X\n",
      shadeId, shade->name, oldAddress);
  } else {
    Serial.println("ERROR: Failed to save shade configuration");
  }
}

void SerialInterface::handleListenCommand(const char* params) {
  String param = String(params);
  param.trim();

  Serial.println("Listening for remote commands...");
  Serial.println("All received remote signals will be displayed.");
  Serial.println("This is useful for discovering remote addresses and commands.");
  Serial.println("Press any key to stop listening.");
  Serial.println();

  // Set listening mode
  listeningMode = true;
  listeningStartTime = millis();
}

void SerialInterface::handleScanCommand(const char* params) {
  Serial.println("Scanning for unknown Somfy remotes...");
  Serial.println("Press buttons on your Somfy remotes to discover their addresses.");
  Serial.println("Only unknown remotes (not paired to any shade) will be highlighted.");
  Serial.println("Press any key to stop scanning.");
  Serial.println();

  // Set scanning mode (similar to listening but focused on unknown remotes)
  scanningMode = true;
  scanningStartTime = millis();

  // Clear the discovered remotes list
  discoveredRemoteCount = 0;
  memset(discoveredRemotes, 0, sizeof(discoveredRemotes));
}

void SerialInterface::handleTestCommand(const char* params) {
  String param = String(params);
  param.trim();

  if (param.length() == 0) {
    Serial.println("Usage: test <shade_id>");
    Serial.println("Example: test 1");
    return;
  }

  uint8_t shadeId = parseShadeId(param.c_str());
  if (!isValidShadeId(shadeId)) {
    Serial.println("Invalid shade ID. Use 'list shades' to see available shades.");
    return;
  }

  SomfyShade* shade = somfy.getShadeById(shadeId);
  if (!shade) {
    Serial.printf("Shade %d not found.\n", shadeId);
    return;
  }

  if (!shade->paired) {
    Serial.printf("Shade %d is not paired to any remote. Use 'pair %d' first.\n", shadeId, shadeId);
    return;
  }

  Serial.printf("Testing communication with shade %d (%s)...\n", shadeId, shade->name);
  Serial.printf("Remote address: 0x%08X\n", shade->getRemoteAddress());
  Serial.printf("Rolling code: %d\n", shade->getRollingCode());
  Serial.println();

  Serial.println("Sending MY command (should jog the shade)...");
  shade->sendCommand(somfy_commands::My);
  delay(1000);

  Serial.println("Test complete. Did the shade respond?");
}

void SerialInterface::handleStatusCommand() {
  Serial.println("=== ESPSomfy-RTS System Status ===");
  Serial.println();

  // System info
  Serial.printf("Free heap: %d bytes\n", ESP.getFreeHeap());
  Serial.printf("Uptime: %lu seconds\n", millis() / 1000);
  Serial.println();

  // Transceiver status
  Serial.println("Transceiver Configuration:");
  Serial.printf("  Frequency: %.2f MHz\n", somfy.transceiver.config.frequency);
  Serial.printf("  TX Power: %d dBm\n", somfy.transceiver.config.txPower);
  Serial.printf("  Enabled: %s\n", somfy.transceiver.config.enabled ? "Yes" : "No");
  Serial.println();

  // Shade summary
  uint8_t totalShades = somfy.shadeCount();
  uint8_t pairedShades = 0;
  for (uint8_t i = 0; i < SOMFY_MAX_SHADES; i++) {
    SomfyShade* shade = &somfy.shades[i];
    if (shade->getShadeId() != 255 && shade->paired) {
      pairedShades++;
    }
  }

  Serial.printf("Shades: %d total, %d paired\n", totalShades, pairedShades);
  Serial.printf("Groups: %d\n", somfy.groupCount());
  Serial.printf("Rooms: %d\n", somfy.roomCount());
  Serial.println();

  // Mode status
  if (pairingMode) {
    Serial.printf("PAIRING MODE: Active for shade %d\n", pairingShadeId);
  }
  if (listeningMode) {
    Serial.println("LISTENING MODE: Active");
  }
  if (scanningMode) {
    Serial.printf("SCANNING MODE: Active (%d remotes discovered)\n", discoveredRemoteCount);
  }

  if (!pairingMode && !listeningMode && !scanningMode) {
    Serial.println("Mode: Normal operation");
  }
}

// Utility function to validate and provide better error messages
bool SerialInterface::validateShadeCommand(uint8_t shadeId, const char* command) {
  SomfyShade* shade = somfy.getShadeById(shadeId);
  if (!shade) {
    Serial.printf("Error: Shade %d not found. Use 'list shades' to see available shades.\n", shadeId);
    return false;
  }

  if (!shade->paired) {
    Serial.printf("Error: Shade %d (%s) is not paired to any remote.\n", shadeId, shade->name);
    Serial.printf("Use 'pair %d' to pair this shade to a remote first.\n", shadeId);
    return false;
  }

  return true;
}

// Enhanced shade command handler with better validation
void SerialInterface::handleShadeCommandEnhanced(uint8_t shadeId, const char* command) {
  if (!validateShadeCommand(shadeId, command)) {
    return;
  }

  // Call the original handler
  handleShadeCommand(shadeId, command);
}
