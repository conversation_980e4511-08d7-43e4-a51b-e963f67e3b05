#ifndef SERIAL_INTERFACE_H
#define SERIAL_INTERFACE_H

#include <Arduino.h>
#include "Somfy.h"

class SerialInterface {
  private:
    char inputBuffer[128];
    uint8_t bufferIndex = 0;
    bool commandReady = false;
    
    // Command parsing helpers
    void parseCommand(const char* command);
    void printHelp();
    void printShadeList();
    void printShadeInfo(uint8_t shadeId);
    void printRemoteInfo(uint32_t address, uint16_t rollingCode, somfy_commands cmd);
    
    // Command handlers
    void handleShadeCommand(uint8_t shadeId, const char* command);
    void handlePairCommand(const char* params);
    void handleListCommand(const char* params);
    void handleInfoCommand(const char* params);
    void handleAddCommand(const char* params);
    void handleDeleteCommand(const char* params);
    void handleSetCommand(const char* params);
    
    // Utility functions
    uint8_t parseShadeId(const char* str);
    float parseFloat(const char* str);
    bool isValidShadeId(uint8_t shadeId);
    
  public:
    SerialInterface();
    void begin(uint32_t baudRate = 115200);
    void loop();
    void processInput();
    
    // Event handlers for remote discovery
    void onRemoteReceived(uint32_t address, uint16_t rollingCode, somfy_commands cmd, int8_t rssi);
    void onShadeStateChanged(uint8_t shadeId, float position, float tiltPosition, int8_t direction);
    void onShadePaired(uint8_t shadeId, uint32_t address);
};

#endif // SERIAL_INTERFACE_H
