#ifndef SOMFY_H
#define SOMFY_H

#include <Arduino.h>
#include <Preferences.h>
#include <ELECHOUSE_CC1101_SRC_DRV.h>
#include <SPI.h>
#include <ArduinoJson.h>

// Forward declarations (for serial interface only)
class SerialInterface;
class JsonResponse;

// Constants
#define SOMFY_MAX_SHADES 32
#define SOMFY_MAX_GROUPS 16
#define SOMFY_MAX_ROOMS 16
#define SOMFY_MAX_LINKED_REMOTES 8
#define SOMFY_MAX_GROUPED_SHADES 16
#define SOMFY_MAX_REPEATERS 16

#define SYMBOL 640
#define TILT_REPEATS 8
#define SETMY_REPEATS 8
#define SOMFY_NO_WIND_REMOTE_TIMEOUT 300000

// Enums
enum class somfy_commands : uint8_t {
  My = 0x1,
  Up = 0x2,
  MyUp = 0x3,
  Down = 0x4,
  MyDown = 0x5,
  UpDown = 0x6,
  MyUpDown = 0x7,
  Prog = 0x8,
  SunFlag = 0x9,
  Flag = 0xA,
  StepUp = 0xB,
  StepDown = 0xC,
  UnknownD = 0xD,
  RTWProto = 0xE,
  Sensor = 0xF,
  Toggle = 0x10,
  Favorite = 0x11,
  Stop = 0x12
};

enum class shade_types : uint8_t {
  roller = 0,
  blind = 1,
  awning = 2,
  shutter = 3,
  drycontact = 4,
  drycontact2 = 5,
  garage1 = 6,
  garage3 = 7,
  ldrapery = 8,
  rdrapery = 9,
  cdrapery = 10,
  lgate = 11,
  rgate = 12,
  cgate = 13,
  lgate1 = 14,
  rgate1 = 15,
  cgate1 = 16
};

enum class tilt_types : uint8_t {
  none = 0,
  tiltmotor = 1,
  integrated = 2,
  euromode = 3,
  tiltonly = 4
};

enum class radio_proto : uint8_t {
  RTS = 0,
  RTW = 1,
  RTV = 2,
  GP_Remote = 3,
  GP_Relay = 4
};

enum class somfy_flags_t : uint8_t {
  None = 0x00,
  Sunny = 0x01,
  Windy = 0x02,
  Light = 0x04
};

enum class gpio_flags_t : uint8_t {
  None = 0x00,
  LowLevelTrigger = 0x01
};

// Structures
struct somfy_frame_t {
  bool valid = false;
  bool processed = false;
  bool hwsync = false;
  uint8_t bitLength = 56;
  uint8_t encKey = 0;
  uint8_t checksum = 0;
  uint8_t stepSize = 0;
  uint8_t repeats = 0;
  int8_t rssi = 0;
  uint8_t lqi = 0;
  somfy_commands cmd = somfy_commands::My;
  uint32_t remoteAddress = 0;
  uint16_t rollingCode = 0;
  radio_proto proto = radio_proto::RTS;
  
  void copy(somfy_frame_t &frame);
  bool isSynonym(somfy_frame_t &frame);
  void decodeFrame(byte *data, uint8_t len);
  void encodeFrame(byte *frame);
  void encode80BitFrame(byte *frame, uint8_t repeat);
  uint8_t encode80Byte7(uint8_t val, uint8_t repeat);
  uint8_t calc80Checksum(uint8_t b7, uint8_t b8, uint8_t b9);
  void encodeRTWFrame(byte *frame);
  void encodeRTVFrame(byte *frame);
};

struct somfy_rx_t {
  uint8_t bit_length = 56;
  uint32_t pulses[200];
  uint16_t pulse_count = 0;
  uint32_t last_pulse = 0;
  bool valid = false;
  bool processed = false;
  int8_t rssi = 0;
  uint8_t lqi = 0;
  
  void clear();
  bool decode(somfy_frame_t *frame);
};

// Forward declarations for classes
class SomfyRemote;
class SomfyLinkedRemote;
class SomfyShade;
class SomfyGroup;
class SomfyRoom;
class SomfyShadeController;
class Transceiver;

// Transceiver configuration structure
struct transceiver_config_t {
  uint8_t type = 56;
  uint8_t TXPin = 12;
  uint8_t RXPin = 14;
  uint8_t SCKPin = 18;
  uint8_t MOSIPin = 23;
  uint8_t MISOPin = 19;
  uint8_t CSNPin = 5;
  float frequency = 433.92;
  float deviation = 47.60;
  float rxBandwidth = 325.0;
  bool enabled = true;
  bool radioInit = false;
  int8_t txPower = 10;
  radio_proto proto = radio_proto::RTS;

  void load();
  void save();
  void apply();
};

// Base remote class
class SomfyRemote {
  protected:
    uint32_t m_remoteAddress = 0;
    char m_remotePrefId[16] = "";

  public:
    uint16_t lastRollingCode = 0;
    uint8_t bitLength = 56;
    radio_proto proto = radio_proto::RTS;

    SomfyRemote();
    virtual ~SomfyRemote() = default;

    void setRemoteAddress(uint32_t address);
    uint32_t getRemoteAddress();
    void setRollingCode(uint16_t code);
    uint16_t getRollingCode();
    void sendCommand(somfy_commands cmd, uint8_t repeat = 1, uint8_t stepSize = 0);
};

// Linked remote class
class SomfyLinkedRemote : public SomfyRemote {
  public:
    SomfyLinkedRemote();
};

// Room class
class SomfyRoom {
  public:
    uint8_t roomId = 0;
    char name[33] = "";
    uint8_t sortOrder = 255;

    void clear();
    void unpublish();
    bool fromJSON(JsonObject &obj);  // Stub for compatibility
};

// Shade class
class SomfyShade : public SomfyRemote {
  private:
    uint8_t shadeId = 255;
    uint8_t m_shadeIds[SOMFY_MAX_SHADES];

  public:
    char name[33] = "";
    uint8_t roomId = 0;
    shade_types shadeType = shade_types::roller;
    tilt_types tiltType = tilt_types::none;
    bool paired = false;
    bool flipCommands = false;
    bool flipPosition = false;
    uint8_t flags = 0;
    uint8_t gpioFlags = 0;
    uint8_t repeats = 1;
    uint8_t sortOrder = 255;
    uint8_t stepSize = 100;
    uint8_t gpioUp = 255;
    uint8_t gpioDown = 255;
    uint8_t gpioMy = 255;
    uint32_t upTime = 10000;
    uint32_t downTime = 10000;
    uint32_t tiltTime = 7000;
    float currentPos = 0.0f;
    float currentTiltPos = 0.0f;
    float target = 0.0f;
    float tiltTarget = 0.0f;
    float myPos = -1.0f;
    float myTiltPos = -1.0f;
    int8_t direction = 0;
    int8_t tiltDirection = 0;

    // Movement tracking
    uint64_t moveStart = 0;
    uint64_t tiltStart = 0;
    uint64_t sunStart = 0;
    uint64_t windStart = 0;
    uint64_t windLast = 0;
    uint64_t noSunStart = 0;
    uint64_t noWindStart = 0;
    bool sunDone = true;
    bool windDone = true;
    bool noSunDone = true;
    bool noWindDone = true;
    bool settingMyPos = false;
    bool settingPos = false;
    bool settingTiltPos = false;
    uint64_t awaitMy = 0;
    float startPos = 0.0f;
    float startTiltPos = 0.0f;

    SomfyLinkedRemote linkedRemotes[SOMFY_MAX_LINKED_REMOTES];
    somfy_frame_t lastFrame;

    SomfyShade();
    void clear();
    void load();
    bool save();

    // Getters/Setters
    uint8_t getShadeId() { return shadeId; }
    void setShadeId(uint8_t id) { shadeId = id; }

    // Movement and control
    void sendCommand(somfy_commands cmd);
    void sendCommand(somfy_commands cmd, uint8_t repeat, uint8_t stepSize = 0);
    void sendTiltCommand(somfy_commands cmd);
    void moveToTarget(float pos, float tilt = -1);
    void moveToTiltTarget(float target);
    void moveToMyPosition();
    void setMyPosition(float pos, float tilt = -1);
    void checkMovement();
    void setMovement(int8_t dir);
    void setTiltMovement(int8_t dir);
    void setGPIOs();

    // Frame processing
    void processFrame(somfy_frame_t &frame, bool internal = false);
    void processInternalCommand(somfy_commands cmd, uint8_t repeat = 1);
    void processWaitingFrame();

    // Remote management
    bool linkRemote(uint32_t address, uint16_t rollingCode);
    bool unlinkRemote(uint32_t address);

    // Utility functions
    bool isIdle();
    bool isAtTarget();
    bool isToggle();
    bool hasSunSensor();
    bool hasLight();
    bool simMy();
    bool isInGroup();
    somfy_commands transformCommand(somfy_commands cmd);
    float transformPosition(float pos);

    // Removed JSON serialization functions for serial interface

    // MQTT/Event emission
    void emitState(const char *evt = "shadeState");
    void emitState(uint8_t num, const char *evt = "shadeState");
    void emitCommand(somfy_commands cmd, const char *source, uint32_t sourceAddress, const char *evt = "shadeCommand");
    void emitCommand(uint8_t num, somfy_commands cmd, const char *source, uint32_t sourceAddress, const char *evt = "shadeCommand");
    void publish(const char *topic, const char *payload, bool retain = false);
    void publish(const char *topic, uint32_t val, bool retain = false);
    void publish(const char *topic, float val, bool retain = false);
    void publish(const char *topic, bool val, bool retain = false);
    void unpublish();
    void commit();

    // Position management
    void p_target(float target);
    void p_tiltTarget(float target);
    void p_currentPos(float pos);
    void p_currentTiltPos(float pos);
    void p_myPos(float pos);
    void p_myTiltPos(float pos);
    void p_direction(int8_t dir);
    void p_tiltDirection(int8_t dir);
    void p_sunFlag(bool flag);
    void commitMyPosition();
};

// Group class
class SomfyGroup : public SomfyRemote {
  private:
    uint8_t groupId = 255;

  public:
    char name[33] = "";
    uint8_t roomId = 0;
    uint8_t flags = 0;
    uint8_t repeats = 1;
    uint8_t sortOrder = 255;
    bool flipCommands = false;
    uint8_t linkedShades[SOMFY_MAX_GROUPED_SHADES] = {0};

    SomfyGroup();
    void clear();

    // Getters/Setters
    uint8_t getGroupId() { return groupId; }
    void setGroupId(uint8_t id) { groupId = id; }

    // Shade management
    bool linkShade(uint8_t shadeId);
    bool unlinkShade(uint8_t shadeId);
    bool hasShadeId(uint8_t shadeId);
    void compressShades();
    void updateFlags();

    // Utility functions
    bool hasSunSensor();

    // Removed JSON serialization functions for serial interface
    void toJSONRef(JsonResponse &json);  // Stub for compatibility

    // MQTT/Event emission
    void emitState(const char *evt = "groupState");
    void emitState(uint8_t num, const char *evt = "groupState");
    void unpublish();
};

// Transceiver class
class Transceiver {
  public:
    transceiver_config_t config;

    bool begin();
    void loop();
    void enableReceive();
    void disableReceive();
    void beginTransmit();
    void endTransmit();
    void sendFrame(byte *frame, uint8_t sync, uint8_t bitLength);
    bool receive(somfy_rx_t *rx);
    void emitFrame(somfy_frame_t *frame, somfy_rx_t *rx);
};

// Main controller class
class SomfyShadeController {
  private:
    uint8_t m_shadeIds[SOMFY_MAX_SHADES];
    bool isDirty = false;
    uint64_t lastCommit = 0;

  public:
    uint32_t startingAddress = 0;
    SomfyShade shades[SOMFY_MAX_SHADES];
    SomfyGroup groups[SOMFY_MAX_GROUPS];
    SomfyRoom rooms[SOMFY_MAX_ROOMS];
    uint32_t repeaters[SOMFY_MAX_REPEATERS] = {0};
    Transceiver transceiver;

    SomfyShadeController();
    bool begin();
    void end();
    void loop();
    void commit();
    bool useNVS();

    // Shade management
    SomfyShade* addShade();
    SomfyShade* addShade(JsonObject &obj);
    bool deleteShade(uint8_t shadeId);
    SomfyShade* getShadeById(uint8_t shadeId);
    SomfyShade* findShadeByRemoteAddress(uint32_t address);
    uint8_t shadeCount();
    int8_t getMaxShadeOrder();
    uint8_t getNextShadeId();

    // Group management
    SomfyGroup* addGroup();
    SomfyGroup* addGroup(JsonObject &obj);
    bool deleteGroup(uint8_t groupId);
    SomfyGroup* getGroupById(uint8_t groupId);
    SomfyGroup* findGroupByRemoteAddress(uint32_t address);
    uint8_t groupCount();
    int8_t getMaxGroupOrder();
    uint8_t getNextGroupId();
    void updateGroupFlags();

    // Room management
    SomfyRoom* getRoomById(uint8_t roomId);
    bool deleteRoom(uint8_t roomId);
    uint8_t roomCount();
    int8_t getMaxRoomOrder();
    uint8_t getNextRoomId();

    // Repeater management
    bool linkRepeater(uint32_t address);
    bool unlinkRepeater(uint32_t address);
    void compressRepeaters();
    uint8_t repeaterCount();

    // Frame processing
    void processFrame(somfy_frame_t &frame, bool internal = false);
    void processWaitingFrame();
    void sendFrame(somfy_frame_t &frame, uint8_t repeat = 1);

    // Utility functions
    uint32_t generateRemoteAddress();

    // Removed JSON serialization functions for serial interface
    void publish();  // Stub for compatibility
    uint32_t getNextRemoteAddress(uint8_t id);  // Missing function

    // MQTT/Event emission
    void emitState(uint8_t num = 255);

    // Legacy support
    void loadLegacy();
};

// Function declarations
String translateSomfyCommand(const somfy_commands cmd);
somfy_commands translateSomfyCommand(const String& string);

#endif // SOMFY_H
