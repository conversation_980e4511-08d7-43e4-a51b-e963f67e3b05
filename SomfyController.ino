#include <LittleFS.h>
#include <esp_task_wdt.h>
#include "Somfy.h"
#include "SerialInterface.h"

// Global objects
SomfyShadeController somfy;
SerialInterface serialInterface;

void setup() {
  // Initialize serial interface first
  serialInterface.begin(115200);

  Serial.println("ESPSomfy-RTS Serial-Only Version");
  Serial.println("Mounting File System...");

  if(LittleFS.begin()) {
    Serial.println("File system mounted successfully");
  } else {
    Serial.println("Error mounting file system");
  }

  // Initialize Somfy controller
  Serial.println("Initializing Somfy RTS controller...");
  if(somfy.begin()) {
    Serial.println("Somfy controller initialized successfully");
  } else {
    Serial.println("Failed to initialize Somfy controller");
  }

  // Initialize watchdog timer
  esp_task_wdt_init(7, true); // Enable panic so ESP32 restarts
  esp_task_wdt_add(NULL); // Add current thread to WDT watch

  Serial.println("System ready!");
  Serial.println("Type 'help' for available commands");
}

void loop() {
  // Reset watchdog timer
  esp_task_wdt_reset();

  // Process serial commands
  serialInterface.loop();

  // Run Somfy controller main loop
  somfy.loop();

  // Small delay to prevent overwhelming the system
  delay(1);
}

// Hook into the Somfy frame processing to emit remote discovery events
void onSomfyFrameReceived(somfy_frame_t* frame, somfy_rx_t* rx) {
  if (frame && frame->valid) {
    // Emit remote discovery information to serial interface
    serialInterface.onRemoteReceived(
      frame->remoteAddress,
      frame->rollingCode,
      frame->cmd,
      rx ? rx->rssi : 0
    );
  }
}
